package com.cjy.pyp.modules.activity.web;

import com.alibaba.fastjson.JSON;
import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.activity.entity.ActivityImageEntity;
import com.cjy.pyp.modules.activity.entity.ActivityTextEntity;
import com.cjy.pyp.modules.activity.service.ActivityImageService;
import com.cjy.pyp.modules.activity.service.ActivityRechargeRecordService;
import com.cjy.pyp.modules.activity.service.ActivityTextService;
import com.cjy.pyp.modules.sys.controller.AbstractController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 活动点评Web接口
 */
@RestController
@RequestMapping("web/activity/review")
public class WebActivityReviewController extends AbstractController {

    @Autowired
    private ActivityTextService activityTextService;

    @Autowired
    private ActivityImageService activityImageService;

    @Autowired
    private ActivityRechargeRecordService activityRechargeRecordService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 获取抖音点评内容
     */
    @GetMapping("/douyin")
    public R getDouyinReview(@RequestParam("activityId") Long activityId) {
        try {
            
        
        // 检查并扣减次数
        R deductResult = activityRechargeRecordService.checkAndDeductCount(
                null,
                activityId,
                3, // 转发类型
                null,
                  "抖音点评"
        );

        if (!deductResult.get("code").equals(200)) {
            return deductResult;
        }
            return getReviewContent(activityId, "douyin_review", "抖音点评");
        } catch (Exception e) {
            return R.error("获取抖音点评内容失败: " + e.getMessage());
        }
    }

    /**
     * 获取大众点评内容
     */
    @GetMapping("/dianping")
    public R getDianpingReview(@RequestParam("activityId") Long activityId) {
        try {
            
        // 检查并扣减次数
        R deductResult = activityRechargeRecordService.checkAndDeductCount(
                null,
                activityId,
                3, // 转发类型
                null,
                  "大众点评"
        );

        if (!deductResult.get("code").equals(200)) {
            return deductResult;
        }
            return getReviewContent(activityId, "dianping", "大众点评");
        } catch (Exception e) {
            return R.error("获取大众点评内容失败: " + e.getMessage());
        }
    }

    /**
     * 获取美团点评内容
     */
    @GetMapping("/meituan")
    public R getMeituanReview(@RequestParam("activityId") Long activityId) {
        try {
            
        // 检查并扣减次数
        R deductResult = activityRechargeRecordService.checkAndDeductCount(
                null,
                activityId,
                3, // 转发类型
                null,
                  "美团点评"
        );

        if (!deductResult.get("code").equals(200)) {
            return deductResult;
        }
            return getReviewContent(activityId, "meituan", "美团点评");
        } catch (Exception e) {
            return R.error("获取美团点评内容失败: " + e.getMessage());
        }
    }

    /**
     * 获取携程点评内容（兼容旧版）
     */
    @GetMapping("/ctrip")
    public R getCtripReview(@RequestParam("activityId") Long activityId,
            @RequestParam(value = "type", required = false) String type) throws Exception {


        // 根据type参数决定使用哪种广告类型
        String adType;
        String platformName;

        if ("notes".equals(type)) {
            adType = "ctrip_notes";
            platformName = "携程笔记";
        } else if ("review".equals(type)) {
            adType = "ctrip_review";
            platformName = "携程点评";
        } else {
            // 默认使用携程点评（兼容旧版）
            adType = "ctrip";
            platformName = "携程点评";
        }
        
        // 检查并扣减次数
        R deductResult = activityRechargeRecordService.checkAndDeductCount(
                null,
                activityId,
                3, // 转发类型
                null,
                 platformName + "转发"
        );

        if (!deductResult.get("code").equals(200)) {
            return deductResult;
        }

        return getReviewContent(activityId, adType, platformName);
    }

    /**
     * 异步重新生成抖音点评内容
     */
    @RequestMapping("/regenerate/douyin")
    public R regenerateDouyinReview(@RequestParam("activityId") Long activityId) {
        try {
            // 检查并扣减次数
            R deductResult = activityRechargeRecordService.checkAndDeductCount(
                    null,
                    activityId,
                    3, // 转发类型
                    null,
                    "抖音点评重新生成"
            );

            if (!deductResult.get("code").equals(200)) {
                return deductResult;
            }

            return regenerateReviewContentAsync(activityId, "douyin_review", "抖音点评");
        } catch (Exception e) {
            return R.error("重新生成抖音点评内容失败: " + e.getMessage());
        }
    }

    /**
     * 异步重新生成大众点评内容
     */
    @RequestMapping("/regenerate/dianping")
    public R regenerateDianpingReview(@RequestParam("activityId") Long activityId) {
        try {
            // 检查并扣减次数
            R deductResult = activityRechargeRecordService.checkAndDeductCount(
                    null,
                    activityId,
                    3, // 转发类型
                    null,
                    "大众点评重新生成"
            );

            if (!deductResult.get("code").equals(200)) {
                return deductResult;
            }

            return regenerateReviewContentAsync(activityId, "dianping", "大众点评");
        } catch (Exception e) {
            return R.error("重新生成大众点评内容失败: " + e.getMessage());
        }
    }

    /**
     * 异步重新生成美团点评内容
     */
    @RequestMapping("/regenerate/meituan")
    public R regenerateMeituanReview(@RequestParam("activityId") Long activityId) {
        try {
            // 检查并扣减次数
            R deductResult = activityRechargeRecordService.checkAndDeductCount(
                    null,
                    activityId,
                    3, // 转发类型
                    null,
                    "美团点评重新生成"
            );

            if (!deductResult.get("code").equals(200)) {
                return deductResult;
            }

            return regenerateReviewContentAsync(activityId, "meituan", "美团点评");
        } catch (Exception e) {
            return R.error("重新生成美团点评内容失败: " + e.getMessage());
        }
    }

    /**
     * 异步重新生成携程点评内容
     */
    @RequestMapping("/regenerate/ctrip")
    public R regenerateCtripReview(@RequestParam("activityId") Long activityId,
            @RequestParam(value = "type", required = false) String type) {
        try {
            // 根据type参数决定使用哪种广告类型
            String adType;
            String platformName;

            if ("notes".equals(type)) {
                adType = "ctrip_notes";
                platformName = "携程笔记";
            } else if ("review".equals(type)) {
                adType = "ctrip_review";
                platformName = "携程点评";
            } else {
                // 默认使用携程点评（兼容旧版）
                adType = "ctrip";
                platformName = "携程点评";
            }

            // 检查并扣减次数
            R deductResult = activityRechargeRecordService.checkAndDeductCount(
                    null,
                    activityId,
                    3, // 转发类型
                    null,
                    platformName + "重新生成"
            );

            if (!deductResult.get("code").equals(200)) {
                return deductResult;
            }

            return regenerateReviewContentAsync(activityId, adType, platformName);
        } catch (Exception e) {
            return R.error("重新生成携程点评内容失败: " + e.getMessage());
        }
    }

    /**
     * 查询任务状态
     */
    @GetMapping("/task/status/{taskId}")
    public R getTaskStatus(@PathVariable("taskId") String taskId) {
        try {
            String statusKey = "review_task_status:" + taskId;
            String resultKey = "review_task_result:" + taskId;

            String status = stringRedisTemplate.opsForValue().get(statusKey);
            if (status == null) {
                return R.error("任务不存在或已过期");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("taskId", taskId);
            result.put("status", status);

            if ("completed".equals(status)) {
                String resultJson = stringRedisTemplate.opsForValue().get(resultKey);
                if (resultJson != null) {
                    // 解析JSON并返回结构化数据
                    Object resultData = JSON.parse(resultJson);
                    result.put("result", resultData);
                }
            } else if ("failed".equals(status)) {
                String errorKey = "review_task_error:" + taskId;
                String error = stringRedisTemplate.opsForValue().get(errorKey);
                result.put("error", error);
            }

            return R.ok().put("result", result);
        } catch (Exception e) {
            return R.error("查询任务状态失败: " + e.getMessage());
        }
    }

    /**
     * 异步重新生成点评内容
     */
    private R regenerateReviewContentAsync(Long activityId, String adType, String platformName) {
        try {
            // 生成任务ID
            String taskId = UUID.randomUUID().toString();

            // 在Redis中设置任务状态为处理中
            String statusKey = "review_task_status:" + taskId;
            stringRedisTemplate.opsForValue().set(statusKey, "processing", 10, TimeUnit.MINUTES);

            // 异步执行生成任务
            CompletableFuture.runAsync(() -> {
                try {
                    // 调用现有的生成方法
                    R result = getReviewContent(activityId, adType, platformName);

                    if (result.get("code").equals(200)) {
                        // 任务成功完成
                        String resultKey = "review_task_result:" + taskId;
                        // 将结果对象序列化为JSON字符串
                        String resultJson = JSON.toJSONString(result.get("result"));
                        stringRedisTemplate.opsForValue().set(resultKey, resultJson, 10, TimeUnit.MINUTES);
                        stringRedisTemplate.opsForValue().set(statusKey, "completed", 10, TimeUnit.MINUTES);
                    } else {
                        // 任务失败
                        String errorKey = "review_task_error:" + taskId;
                        String errorMsg = result.get("msg") != null ? result.get("msg").toString() : "生成失败";
                        stringRedisTemplate.opsForValue().set(errorKey, errorMsg, 10, TimeUnit.MINUTES);
                        stringRedisTemplate.opsForValue().set(statusKey, "failed", 10, TimeUnit.MINUTES);
                    }
                } catch (Exception e) {
                    // 任务异常
                    String errorKey = "review_task_error:" + taskId;
                    stringRedisTemplate.opsForValue().set(errorKey, e.getMessage(), 10, TimeUnit.MINUTES);
                    stringRedisTemplate.opsForValue().set(statusKey, "failed", 10, TimeUnit.MINUTES);
                }
            });

            return R.ok().put("taskId", taskId);
        } catch (Exception e) {
            return R.error("启动异步任务失败: " + e.getMessage());
        }
    }

    /**
     * 通用获取点评内容方法
     *
     * @throws Exception
     */
    private R getReviewContent(Long activityId, String adType, String platformName) throws Exception {
        Map<String, Object> result = new HashMap<>();

        // 查询对应平台的文案 - 使用现有的方法
        ActivityTextEntity textEntity = activityTextService.findByActivityIdAndAdTypeNotUse(activityId, adType);

        // 查询对应平台的图片 - 使用按平台的方法
        List<ActivityImageEntity> imageList = activityImageService.findByActivityIdNoUseLimitByPlatform(activityId, adType, 3);

        // 构建返回数据
        result.put("platform", platformName);
        result.put("platformCode", adType);

        if (textEntity != null) {
            result.put("title", textEntity.getName() != null ? textEntity.getName() : textEntity.getTitle());
            result.put("content", textEntity.getResult());
            result.put("promptKeyword", textEntity.getQuery());
            result.put("textId", textEntity.getId());
            activityTextService.incrementUseCount(textEntity.getId());
        } else {
            result.put("title", "");
            result.put("content", "");
            result.put("promptKeyword", "");
            result.put("textId", null);
        }

        if (!imageList.isEmpty()) {
            result.put("images", imageList);
            result.put("hasImages", true);
            imageList.forEach(e -> {
                activityImageService.incrementUseCountByPlatform(e.getId(), adType, activityId);
            });
        } else {
            result.put("images", null);
            result.put("hasImages", false);
        }

        return R.ok().put("result", result);
    }
}
